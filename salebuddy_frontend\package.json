{"name": "salebuddy_frontend", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@material-table/core": "^6.4.4", "@mui/icons-material": "^6.4.8", "@mui/material": "^6.4.8", "@mui/styled-engine-sc": "^6.4.6", "@mui/styles": "^6.4.8", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.8.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-quill-new": "^3.4.6", "react-router": "^7.5.0", "react-router-dom": "^7.5.0", "react-scripts": "5.0.1", "react-slick": "^0.30.3", "slick-carousel": "^1.8.1", "styled-components": "^6.1.16", "sweetalert2": "^11.17.2", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}