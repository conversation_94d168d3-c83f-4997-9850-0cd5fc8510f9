import { Button, Divider, Rating, Typography } from '@mui/material'
import React from 'react'
import useMediaQuery from '@mui/material/useMediaQuery'



export default function OrderSummary() {
    const lg = useMediaQuery('(max-width:1024px)');

    return (
        <div style={{ maxWidth: "100%", background: "#FFFFFF", borderRadius: 8, display:"flex", flexDirection: "column", flexDirection: "column", gap: 10, padding: 20,  maxHeight: 150, position: "sticky", alignSelf: "self-start", top: 130 }}>
            <Typography sx={{ fontWeight: 700, fontSize: 18 }}>Order Summary ( 2 items )</Typography>
            <div style={{ display: "flex", justifyContent: "space-between" }}>
                <Typography>Original Price</Typography>
                <Typography>₹186,998.00</Typography>
            </div>
            <div style={{ display: "flex", justifyContent: "space-between" }}>
                <Typography>Total</Typography>
                <Typography>₹186,998.00</Typography>
            </div>
            <Button variant="contained" sx={{ background: "#12DAA8", color: "black", fontWeight: 600, fontSize: 12 }}>Checkout</Button>

        </div>
    )
}
