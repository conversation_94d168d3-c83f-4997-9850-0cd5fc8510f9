import * as React from 'react';
import useMediaQuery from '@mui/material/useMediaQuery';
import { useTheme } from '@mui/material/styles';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import FilterListIcon from '@mui/icons-material/FilterList';
import { useEffect } from 'react';
import CloseIcon from '@mui/icons-material/Close';
import { useState } from 'react';
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';

const sortOptions = [
  'Top Rated', 'Price (Lowest First)', 'Latest Arrival',
  'Discount (Descending)', 'Featured', 'Price (Highest First)'
];

export default function DropDowns() {

  const theme = useTheme();
  const large = useMediaQuery(theme.breakpoints.down('lg'));

  const [sortname, setSortName] = useState('');

  const [showfilter, SetShowFilter] = useState([]);

  const [category, setCategory] = React.useState([]);
  const [brand, setBrand] = React.useState('');
  const [price, setPrice] = React.useState('');
  const [delivery, setDelivery] = React.useState('');
  const [storage, setStorage] = React.useState('');
  const [sortBy, setSortBy] = React.useState('');

  


  var datac = {
    id: 1,
    name:" Android Phones , I phones , Mac Book , Windows Laptop"
  }

  var datab = {
    id: 1,
    name:" Samsung,OnePlus,Vivo,Realme,Oppo,Nothing,Google,Apple,Xiaomi,Dell,HP,Apple,Asus"
  }

  var datap = {
    id: 1,
    name:" ₹5,000 - ₹10,000; ₹10,001 - ₹15,000; ₹15,001 - ₹20,000; ₹20,001 - ₹30,000; ₹30,001 - ₹40,000; ₹40,001 - ₹50,000; ₹50,001 - ₹75,000;  ₹75,001 - ₹1,00,000; ₹1,00,000 and above"
  }

  var datas = {
    id: 1,
    name: "8GB, 16GB, 32GB, 64GB, 128GB, 256GB, 512GB, 1TB"
  }

  var datad = {
    id: 1,
    name: `Home Delevery , Take In `
  }

  var datasort = {
    id: 1,
    name:" Lattest Arrival , Discount(Descending) , Featured , Price(Highest First) , Top Rated , Price(Lowest First)"
  }

  var categories = datac.name.split(',');
  var brands = datab.name.split(',');
  var prices = datap.name.split(';');
  var storages = datas.name.split(',');
  var delevery = datad.name.split(',');
  var sort = datasort.name.split(',');


  const handlesortName = (item) => {
    setSortName(item);
  }

  const handleFilterName = (item) => {
    SetShowFilter((prev) =>
      prev.includes(item) ? prev : [...prev, item]
    );
  }

  const fillcategory = () => {
    return categories.map((item, i) => {
      return (
        <div onClick={() => handleFilterName(item)} style={{ display: 'flex', gap: 10, padding: 5, justifyContent: 'start' }}><CheckBoxOutlineBlankIcon style={{ color: '#979797', fontWeight: 'lighter' }} /><span>{item}</span></div>
      )
    })
  }

  const fillcategoryTwo = () => {
    return categories.map((item, i) => {
      return (
        <div onClick={() => handleFilterName(item)} style={{ display: 'flex', gap: 10, padding: 5, justifyContent: 'start' }}><CheckBoxOutlineBlankIcon style={{ color: '#979797', fontWeight: 'lighter' }} /><span>{item}</span></div>
      )
    })
  }



  const fillbrands = () => {
    return brands.map((item, i) => {
      return (
        <div onClick={() => handleFilterName(item)} style={{ display: 'flex', gap: 10, padding: 5, justifyContent: 'start' }}><CheckBoxOutlineBlankIcon style={{ color: '#979797', fontWeight: 'lighter' }} /><span>{item}</span></div>
      )
    })
  }

  const fillbrandsTwo = () => {
    return brands.map((item, i) => {
      return (
        <div onClick={() => handleFilterName(item)} style={{ display: 'flex', gap: 10, padding: 5, justifyContent: 'start' }}><CheckBoxOutlineBlankIcon style={{ color: '#979797', fontWeight: 'lighter' }} /><span>{item}</span></div>
      )
    })
  }

  const fillprices = () => {
    return prices.map((item, i) => {
      return (
        <div onClick={() => handleFilterName(item)} style={{ display: 'flex', gap: 10, padding: 5, justifyContent: 'start' }}><CheckBoxOutlineBlankIcon style={{ color: '#979797', fontWeight: 'lighter' }} /><span>{item}</span></div>
      )
    })
  }

  const fillpricesTwo = () => {
    return prices.map((item, i) => {
      return (
        <div onClick={() => handleFilterName(item)} style={{ display: 'flex', gap: 10, padding: 5, justifyContent: 'start' }}><CheckBoxOutlineBlankIcon style={{ color: '#979797', fontWeight: 'lighter' }} /><span>{item}</span></div>
      )
    })
  }

  const fillstorage = () => {
    return storages.map((item, i) => {
      return (
        <div onClick={() => handleFilterName(item)} style={{ display: 'flex', gap: 10, padding: 5, justifyContent: 'start' }}><CheckBoxOutlineBlankIcon style={{ color: '#979797', fontWeight: 'lighter' }} /><span>{item}</span></div>
      )
    })
  }

  const fillstorageTwo = () => {
    return storages.map((item, i) => {
      return (
        <div onClick={() => handleFilterName(item)} style={{ display: 'flex', gap: 10, padding: 5, justifyContent: 'start' }}><CheckBoxOutlineBlankIcon style={{ color: '#979797', fontWeight: 'lighter' }} /><span>{item}</span></div>
      )
    })
  }

  const filldelivery = () => {
    return delevery.map((item, i) => {
      return (
        <div onClick={() => handleFilterName(item)} style={{ display: 'flex', gap: 10, padding: 5, justifyContent: 'start' }}><CheckBoxOutlineBlankIcon style={{ color: '#979797', fontWeight: 'lighter' }} /><span>{item}</span></div>
      )
    })
  }

  const filldeliveryTwo = () => {
    return delevery.map((item, i) => {
      return (
        <div onClick={() => handleFilterName(item)} style={{ display: 'flex', gap: 10, padding: 5, justifyContent: 'start' }}><CheckBoxOutlineBlankIcon style={{ color: '#979797', fontWeight: 'lighter' }} /><span>{item}</span></div>
      )
    })
  }

  const fillSortBy = () => {
    return sort.map((item, i) => {
      return (
        <div onClick={() => handlesortName(item)} style={{ display: 'flex', gap: 10, padding: 5, justifyContent: 'start' }}>{item}</div>
      )
    })
  }

  const showFilterFunction = () => {
    
      return (
        <div style={{ color: '#000', display:'flex'  , flexDirection:'column', height:'100%' ,   justifyContent:'space-between' ,width:'100%' }} >
          
          
          <div style={{ overflowY:'scroll'  , overflowX:'hidden'}}>

            <div style={{ padding: '35px 35px 20px 35px' , display:'flex', justifyContent:'space-between'}} >
            <div style={{fontSize:22 , fontWeight:700}} >All Filter</div>
            <div style={{color:"#000" , cursor:'pointer' , fontSize:'large'}} onClick={()=>closeFilter()} ><CloseIcon/></div>
            </div>

          <div style={{width:'90%' , margin:'auto' ,  height:1 , background:"#cbc2c2" } }  ></div>

          <div  style={{ padding: '35px 35px 20px 35px' , display:'flex', justifyContent:'space-between'}} onClick={()=>handleCategoryTwo()} >
            <div style={{fontSize:20}} >Categories</div>
            <div style={{color:"#000" , fontSize:'large'}}   > 
              {(opentwo == 'false') ?
                <span ><ExpandLessIcon style={{ cursor: 'pointer', color:'#000'  }} /></span>
                : <span ><ExpandMoreIcon style={{ cursor: 'pointer', color: '#000'  }} /></span>
              }
              
            </div>
          </div>
          {(opentwo == 'true') ? <div style={{  padding: '0px 5px 20px 35px' , width: '100%', height: 'auto' ,  color: '#000', fontSize: 18 }} >{fillcategoryTwo()}</div> : <></>}


          <div style={{width:'90%' , margin:'auto' ,  height:1 , background:"#cbc2c2" } }  ></div>

          <div style={{ padding: '35px 35px 20px 35px' , display:'flex', justifyContent:'space-between'}} onClick={()=>handleBrandsTwo()} >
            <div style={{fontSize:20}} >Brand</div>
            <div style={{color:"#000" , fontSize:'large'}}>
              {(openbtwo == 'false') ?
                <span ><ExpandLessIcon style={{ cursor: 'pointer', color:'#000'  }} /></span>
                : <span ><ExpandMoreIcon style={{ cursor: 'pointer', color: '#000'  }} /></span>
              }
            </div>
          </div>
          {(openbtwo == 'true') ? <div style={{  padding: '0px 5px 20px 35px' , width: '100%', height: 'auto' ,  color: '#000', fontSize: 18 }} >{fillbrandsTwo()}</div> : <></>}


          <div style={{width:'90%' , margin:'auto' ,  height:1 , background:"#cbc2c2" } }  ></div>

          <div style={{ padding: '35px 35px 20px 35px' , display:'flex', justifyContent:'space-between'}} onClick={()=>handleDeleveryTwo()} >
            <div style={{fontSize:20}} >Delevery Mode</div>
            <div style={{color:"#000" , fontSize:'large'}}>
              {(opendtwo == 'false') ?
                <span ><ExpandLessIcon style={{ cursor: 'pointer', color:'#000'  }} /></span>
                : <span ><ExpandMoreIcon style={{ cursor: 'pointer', color: '#000'  }} /></span>
              }
            </div>
          </div>
          {(opendtwo == 'true') ? <div style={{  padding: '0px 5px 20px 35px' , width: '100%', height: 'auto' ,  color: '#000', fontSize: 18 }} >{filldeliveryTwo()}</div> : <></>}


          <div style={{width:'90%' , margin:'auto' ,  height:1 , background:"#cbc2c2" } }  ></div>

          <div style={{ padding: '35px 35px 20px 35px' , display:'flex', justifyContent:'space-between'}} onClick={()=>handlePriceTwo()} >
            <div style={{fontSize:20}} >Price</div>
            <div style={{color:"#000" , fontSize:'large'}}>
              {(openptwo == 'false') ?
                <span ><ExpandLessIcon style={{ cursor: 'pointer', color:'#000'  }} /></span>
                : <span ><ExpandMoreIcon style={{ cursor: 'pointer', color: '#000'  }} /></span>
              }
            </div>
          </div>
          {(openptwo == 'true') ? <div style={{  padding: '0px 5px 20px 35px' , width: '100%', height: 'auto' ,  color: '#000', fontSize: 18 }} >{fillpricesTwo()}</div> : <></>}


          <div style={{width:'90%' , margin:'auto' ,  height:1 , background:"#cbc2c2" } }  ></div>

          <div style={{ padding: '35px 35px 20px 35px' , display:'flex', justifyContent:'space-between'}}  onClick={()=>handleStorageTwo()}>
            <div style={{fontSize:20 }} >Internal Storage</div>
            <div style={{color:"#000" , fontSize:'large'}}>
              {(openstwo == 'false') ?
                <span ><ExpandLessIcon style={{ cursor: 'pointer', color:'#000'  }} /></span>
                : <span ><ExpandMoreIcon style={{ cursor: 'pointer', color: '#000'  }} /></span>
              }
            </div>
          </div>
            {(openstwo == 'true') ? <div style={{  padding: '0px 5px 20px 35px' , width: '100%', height: 'auto' ,  color: '#000', fontSize: 18 }} >{fillstorageTwo()}</div> : <></>}
      

          <div style={{width:'90%' , margin:'auto' ,  height:1 , background:"#cbc2c2" } }  ></div>
          
          </div>
          

          <div style={{   padding: '35px 35px 20px 35px' , fontSize:20, fontWeight:900 ,display:'flex', justifyContent:'space-between'}}>
            <div style={{width:'41%', display:'flex' , justifyContent:'center', alignItems:'center' , height:50, background:"#1c1c1c" , color:'#fff' , borderRadius:'5px'  }}>
                Clear All
            </div>

            <div style={{width:'41%', color:'#fff' , display:'flex' , justifyContent:'center', alignItems:'center' , height:50, background:"#12daa8" , color:'#fff' , borderRadius:'5px'  }} >
                Apply
            </div>
          </div>    

        
        </div>
      )
    
  }

  const [open, setOpen] = useState('false')
  const [openb, setOpenB] = useState('false')
  const [openp, setOpenP] = useState('false')
  const [opens, setOpenS] = useState('false')
  const [opend, setOpenD] = useState('false')
  const [opensort, setOpenSort] = useState('false');
  const [opentwo, setOpenTwo] = useState('false')
  const [openbtwo, setOpenBTwo] = useState('false')
  const [openptwo, setOpenPTwo] = useState('false')
  const [openstwo, setOpenSTwo] = useState('false')
  const [opendtwo, setOpenDTwo] = useState('false')
  const [opensorttwo, setOpenSortTwo] = useState('false');
  const [openfilter, setOpenFliter] = useState('false');

  useEffect(() => {
    return () => {
      document.body.style.overflow = '';
    };
  }, [openfilter]);

  const handleCategory = () => {
    if (open == 'false') {
      setOpen('true')
      setOpenB('false');
      setOpenD('false');
      setOpenP('false');
      setOpenS('false');
      setOpenSort('false');
      setOpenFliter('false');

    }
    else {
      setOpen('false');
    }
  }

  const handleCategoryTwo = () =>{ 
    if (opentwo == 'false') {
      setOpenTwo('true')
      setOpenBTwo('false');
      setOpenDTwo('false');
      setOpenPTwo('false');
      setOpenSTwo('false');
      setOpenSortTwo('false');

    }
    else {
      setOpenTwo('false');
    } 
  }


  const handleBrands = () => {
    if (openbtwo == 'false') {
      setOpenB('true')
      setOpenD('false');
      setOpenP('false');
      setOpenS('false');
      setOpen('false');
      setOpenSort('false');
      setOpenFliter('false');

    }
    else {
      setOpenB('false');
    }
  }

  const handleBrandsTwo=()=>{
    if (openbtwo == 'false') {
      setOpenBTwo('true')
      setOpenDTwo('false');
      setOpenPTwo('false');
      setOpenSTwo('false');
      setOpenTwo('false');
      setOpenSortTwo('false');
      
    }
    else {
      setOpenBTwo('false');
    }
  }

  const handlePrice = () => {
    if (openp == 'false') {
      setOpenP('true')
      setOpen('false');
      setOpenB('false');
      setOpenD('false');
      setOpenS('false');
      setOpenSort('false');
      setOpenFliter('false');
    }
    else {
      setOpenP('false');
    }
  }

  const handlePriceTwo=()=>{
    if (openptwo == 'false') {
      setOpenPTwo('true')
      setOpenTwo('false');
      setOpenBTwo('false');
      setOpenDTwo('false');
      setOpenSTwo('false');
      setOpenSortTwo('false');
    }
    else {
      setOpenPTwo('false');
    }
  }

  const handleStorage = () => {
    if (opens == 'false') {
      setOpenS('true')
      setOpen('false');
      setOpenB('false');
      setOpenD('false');
      setOpenP('false');
      setOpenSort('false');
      setOpenFliter('false');
    }
    else {
      setOpenS('false');
    }
  }

  const handleStorageTwo=()=>{
    if (openstwo == 'false') {
      setOpenSTwo('true')
      setOpenTwo('false');
      setOpenBTwo('false');
      setOpenDTwo('false');
      setOpenPTwo('false');
      setOpenSortTwo('false');
    }
    else {
      setOpenSTwo('false');
    }
  }

  const handleDelevery = () => {
    if (opend == 'false') {
      setOpenD('true');
      setOpen('false');
      setOpenB('false');
      setOpenP('false');
      setOpenS('false');
      setOpenSort('false');
      setOpenFliter('false');
    }
    else {
      setOpenD('false');
    }
  }

  const handleDeleveryTwo = () =>{
    if (opendtwo == 'false') {
      setOpenDTwo('true');
      setOpenTwo('false');
      setOpenBTwo('false');
      setOpenPTwo('false');
      setOpenSTwo('false');
      setOpenSortTwo('false');
     
    }
    else {
      setOpenDTwo('false');
    }
  }


  const handleSort = () => {
    if (opensort == 'false') {
      setOpenSort('true');
      setOpen('false');
      setOpenB('false');
      setOpenP('false');
      setOpenS('false');
      setOpenD('false');

    }
    else {
      setOpenSortTwo('false');
    }
  }


  const handlefilter = () => {
    if (openfilter == 'false') {

      setOpenFliter('true');
      setOpenSort('flase');
      setOpen('false');
      setOpenB('false');
      setOpenP('false');
      setOpenS('false');
      setOpenD('false');

    }
    else {
      setOpenFliter('false');
    }
  }

  const closeFilter=()=>{
    setOpenFliter('false');
  }


  return (

    <div style={{ height: '90px', paddingBottom: 20, width: large ? '95%' : "1135px", gap: 20, background: 'red', flexDirection: 'column', display: 'flex', justifyContent: 'space-between', background: '#191919', padding: '10px' }}>

      <div style={{ display: 'flex', overflowX: 'auto', scrollbarWidth: 'none', width: large ? '100%' : '1135px', justifyContent: large ? '' : 'space-between' }}>
        <div style={{ gap: 20, display: 'flex' }}>
          <div style={{ background: '#393939', color: '#fff', height: '35px', borderRadius: '5px', width: '130px' }}>

            <div onClick={handleCategory} style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <div style={{ paddingLeft: 10, fontSize: 'small', fontWeight: 600, color: (open == 'false') ? '#fff' : '#18DAA8' }} >Categories</div>

              <div style={{ fontSize: 8, fontWeight: 600, paddingRight: 10, paddingTop: 5 }}>{(open == 'false') ?
                <span  ><ExpandLessIcon style={{ cursor: 'pointer', color: (open == 'false') ? '#fff' : '#18DAA8' }} /></span>
                : <span  ><ExpandMoreIcon style={{ cursor: 'pointer', color: (open == 'false') ? '#fff' : '#18DAA8' }} /></span>
              }</div>
            </div>

            {(open == 'true') ? <div style={{ zIndex: 1000, position: 'absolute', marginBottom: 50, background: '#393939', width: '190px', height: '140px', boxShadow: '0px 0px 5px rgb(0, 0, 0)', marginTop: 10, color: '#fff', fontSize: 14, borderRadius: '5px', fontWeight: 600 }} >{fillcategory()}</div> : <></>}

          </div>


          <div style={{ background: '#393939', color: '#fff', height: '35px', borderRadius: '5px', width: '110px' }}>

            <div onClick={handleBrands} style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <div style={{ paddingLeft: 10, fontSize: 'small', fontWeight: 600, color: (openb == 'false') ? '#fff' : '#18DAA8' }} >Brands</div>

              <div style={{ fontSize: 8, fontWeight: 600, paddingRight: 10, paddingTop: 5 }}>{(openb == 'false') ?
                <span  ><ExpandLessIcon style={{ cursor: 'pointer', color: (openb == 'false') ? '#fff' : '#18DAA8' }} /></span>
                : <span  ><ExpandMoreIcon style={{ cursor: 'pointer', color: (openb == 'false') ? '#fff' : '#18DAA8' }} /></span>
              }</div>
            </div>

            {(openb == 'true') ? <div style={{ zIndex: 1000, position: 'absolute', marginBottom: 50, background: '#393939', width: '190px', height: 'auto', boxShadow: '0px 0px 5px rgb(0, 0, 0)', marginTop: 10, color: '#fff', fontSize: 14, borderRadius: '5px', fontWeight: 600 }} >{fillbrands()}</div> : <></>}

          </div>


          <div style={{ background: '#393939', color: '#fff', height: '35px', borderRadius: '5px', width: '150px' }}>

            <div onClick={handlePrice} style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <div style={{ paddingLeft: 10, fontSize: 'small', fontWeight: 600, color: (openp == 'false') ? '#fff' : '#18DAA8' }} >Price Range</div>

              <div style={{ fontSize: 8, fontWeight: 600, paddingRight: 10, paddingTop: 5 }}>{(openp == 'false') ?
                <span  ><ExpandLessIcon style={{ cursor: 'pointer', color: (openp == 'false') ? '#fff' : '#18DAA8' }} /></span>
                : <span  ><ExpandMoreIcon style={{ cursor: 'pointer', color: (openp == 'false') ? '#fff' : '#18DAA8' }} /></span>
              }</div>
            </div>

            {(openp == 'true') ? <div style={{ zIndex: 1000, position: 'absolute', marginBottom: 50, background: '#393939', width: '190px', height: 'auto', boxShadow: '0px 0px 5px rgb(0, 0, 0)', marginTop: 10, color: '#fff', fontSize: 14, borderRadius: '5px', fontWeight: 600 }} >{fillprices()}</div> : <></>}

          </div>



          <div style={{ background: '#393939', color: '#fff', height: '35px', borderRadius: '5px', width: '130px' }}>

            <div onClick={handleStorage} style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <div style={{ paddingLeft: 10, fontSize: 'small', fontWeight: 600, color: (opens == 'false') ? '#fff' : '#18DAA8' }} >Storage</div>

              <div style={{ fontSize: 8, fontWeight: 600, paddingRight: 10, paddingTop: 5 }}>{(opens == 'false') ?
                <span  ><ExpandLessIcon style={{ cursor: 'pointer', color: (opens == 'false') ? '#fff' : '#18DAA8' }} /></span>
                : <span  ><ExpandMoreIcon style={{ cursor: 'pointer', color: (opens == 'false') ? '#fff' : '#18DAA8' }} /></span>
              }</div>
            </div>

            {(opens == 'true') ? <div style={{ zIndex: 1000, position: 'absolute', marginBottom: 50, background: '#393939', width: '190px', height: 'auto', boxShadow: '0px 0px 5px rgb(0, 0, 0)', marginTop: 10, color: '#fff', fontSize: 14, borderRadius: '5px', fontWeight: 600 }} >{fillstorage()}</div> : <></>}

          </div>



          <div style={{ background: '#393939', color: '#fff', height: '35px', borderRadius: '5px', width: '150px' }}>

            <div onClick={handleDelevery} style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <div style={{ paddingLeft: 10, fontSize: 'small', fontWeight: 600, color: (opend == 'false') ? '#fff' : '#18DAA8' }} >Delevery Type</div>

              <div style={{ fontSize: 8, fontWeight: 600, paddingRight: 10, paddingTop: 5 }}>{(opend == 'false') ?
                <span  ><ExpandLessIcon style={{ cursor: 'pointer', color: (opend == 'false') ? '#fff' : '#18DAA8' }} /></span>
                : <span  ><ExpandMoreIcon style={{ cursor: 'pointer', color: (opend == 'false') ? '#fff' : '#18DAA8' }} /></span>
              }</div>
            </div>

            {(opend == 'true') ? <div style={{ zIndex: 1000, position: 'absolute', marginBottom: 50, background: '#393939', width: '190px', height: 'auto', boxShadow: '0px 0px 5px rgb(0, 0, 0)', marginTop: 10, color: '#fff', fontSize: 14, borderRadius: '5px', fontWeight: 600 }} >{filldelivery()}</div> : <></>}

          </div>


          <div style={{ background: '#393939', color: '#fff', height: '35px', borderRadius: '5px', width: '110px' }}>

            <div onClick={handlefilter} style={{ display: 'flex', paddingTop: 4, justifyContent: 'space-between', alignItems: 'center' }}>
              <div style={{ paddingLeft: 10, fontSize: 'small', fontWeight: 600, color: (openfilter == 'false') ? '#fff' : '#18DAA8' }} >All Type</div>

              <div style={{ fontSize: 6, fontWeight: 600, paddingRight: 10, paddingTop: 5, color: (openfilter == 'false') ? '#fff' : '#18DAA8' }}><FilterListIcon style={{ fontSize: 18 }} /></div>
            </div>

            {(openfilter == 'true') ? 
            <div>
              <div style={{ position: 'absolute', zIndex: 2000, top:0 , right:0 , scrollBehavior: 'none', width: '30% ', height: '100vh', background: '#fff' }} >{showFilterFunction()}</div>
              <div style={{ position: 'absolute', zIndex: 2000, top:0 , left:0 , scrollBehavior: 'none', width: '70% ', height: '100vh', background: 'rgba(0,0,0,0.5)' }} onClick={closeFilter} ></div>
            </div> : <></>}


          </div>

        </div>



        <div style={{ paddingLeft: large ? '20px' : '' }}>
          <div style={{ background: '#393939', color: '#fff', height: '35px', borderRadius: '5px', width: 'auto' }}>

            <div onClick={handleSort} style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <div style={{ paddingLeft: 10, fontSize: 'small', fontWeight: 600, color: (opensort == 'false') ? '#fff' : '#18DAA8' }} >Sort By <span style={{ fontWeight: 700 }}>{sortname}</span> </div>

              <div style={{ fontSize: 8, fontWeight: 600, paddingRight: 10, paddingTop: 5 }}>{(opensort == 'false') ?
                <span  ><ExpandLessIcon style={{ cursor: 'pointer', color: (opensort == 'false') ? '#fff' : '#18DAA8' }} /></span>
                : <span  ><ExpandMoreIcon style={{ cursor: 'pointer', color: (opensort == 'false') ? '#fff' : '#18DAA8' }} /></span>
              }</div>
            </div>

            {(opensort == 'true') ?  <div style={{ zIndex: 1000, paddingLeft: 5, position: 'absolute', marginBottom: 50, background: '#393939', width: '190px', height: 'auto', boxShadow: '0px 0px 5px rgb(0, 0, 0)', marginTop: 10, color: '#fff', fontSize: 14, borderRadius: '5px', fontWeight: 600 }} >{fillSortBy()}</div> : <></>}

          </div>
        </div>
      </div>

    </div>
  )


}