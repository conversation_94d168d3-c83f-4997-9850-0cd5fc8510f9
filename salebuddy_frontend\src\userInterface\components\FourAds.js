import { Grid2, Typography } from '@mui/material'
import React from 'react'
import FourAdCard from './FourAdCard'
import useMediaQuery from '@mui/material/useMediaQuery';


export default function FourAds() {
    const md = useMediaQuery('(max-width:900px)');

    return (
        <div style={{}}>
            <Typography sx={{paddingX:5,fontSize:28,fontWeight:550,marginBottom:2}}>Deals of the Day</Typography>
            <Grid2 container spacing={2} sx={{ display: "flex", paddingX: 3 }}>
                {Array(4).fill("").map((item, index) => {
                    return (
                        <Grid2 size={md?6:3} key={index}>
                            <FourAdCard />
                        </Grid2>
                    )
                })}
            </Grid2>
        </div>
    )
}
