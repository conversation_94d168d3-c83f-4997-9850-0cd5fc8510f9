import React from 'react'
import SearchIcon from '@mui/icons-material/Search';
import useMediaQuery from '@mui/material/useMediaQuery'



export default function Searchbar() {
  const matches = useMediaQuery('(max-width:780px)');
  return (
    <div style={{ background: "#fff", width: "100%",...(matches ? {} : { maxWidth: '450px' }), height: "100%", paddingLeft: 8, paddingRight: 8, borderRadius: 6, display: matches ? "hidden" : "flex", alignItems: "center", gap: 2 }}>
      <input
        style={{ flexGrow: 1, outline: "none", border: "0", fontSize: 16, paddingLeft: 20 }}
        placeholder='What are you looking for?' />
      <SearchIcon sx={{ color: "black" }} />

    </div>
  )
}
