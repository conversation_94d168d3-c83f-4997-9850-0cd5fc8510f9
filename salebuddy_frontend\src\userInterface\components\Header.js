import * as React from 'react';
import AppBar from '@mui/material/AppBar';
import Box from '@mui/material/Box';
import Toolbar from '@mui/material/Toolbar';
import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';
import IconButton from '@mui/material/IconButton';
import MenuIcon from '@mui/icons-material/Menu';
import ShoppingCartIcon from '@mui/icons-material/ShoppingCart';
import EditIcon from '@mui/icons-material/Edit';
import PersonIcon from '@mui/icons-material/Person';
import LocationIcon from '@mui/icons-material/Room';
import Searchbar from './Searchbar';
import useMediaQuery from '@mui/material/useMediaQuery'
import { useTheme } from '@mui/material/styles';

export default function Header() {
    const theme = useTheme();
    const matches = useMediaQuery('(max-width:780px)');

    return (
        <Box>
            <AppBar  position="fixed" sx={{ background: "black", width: "100%", paddingY: "8px" }}>
                <Toolbar disableGutters="true" sx={{ marginX: "auto", paddingX: "10px",height:"64px", boxSizing: "border-box", width: "100%", maxWidth: "1200px", display: "flex", alignItems: "center", transition: 'margin-left 0.9s ease-in-out, margin-right 0.9s ease-in-out'}}>
                    < div style={{ display: "flex", alignItems: "center" }} >
                        <IconButton
                            size="small"
                            edge="start"
                            color="inherit"
                            aria-label="menu"


                        >
                            <MenuIcon sx={{ fontSize: 40 }} />
                        </IconButton>
                        <Typography variant={matches ? "h5" : "h4"} component="span">
                            SalesBuddy
                        </Typography>
                    </div>
                    <div style={{ display: "flex", alignItems: "center", flexGrow: 1, justifyContent: matches ? "flex-end" : "space-between", marginLeft: "20px" }} >
                        <div style={{ flexGrow: "1", height: "45px", display: matches ? "none" : "flex", justifyContent: "center" }}>
                            <Searchbar />
                        </div>
                        <div style={{ display: "flex", alignItems: "center", gap: 20, marginLeft: "10px" }}>
                            <div style={{ display: matches ? "none" : "flex", alignItems: "center", gap: 2 }}>
                                <LocationIcon sx={{ fontSize: 26 }} />
                                <div style={{ display: "flex", gap: 4, alignItems: "center" }}>
                                    <p style={{ fontSize: 14, fontWeight: 600 }}>Gwalior,475001</p>
                                    <EditIcon sx={{ fontSize: 14 }} />
                                </div>
                            </div>
                            <PersonIcon sx={{ fontSize: 28, cursor: "pointer" }} />
                            <ShoppingCartIcon sx={{ fontSize: 28, cursor: "pointer" }} />
                        </div>
                    </div>

                </Toolbar>
                <div style={{display:matches?"flex":"none", flexGrow: "1", height: "45px",alignItems:"center",justifyContent:"center",paddingLeft:"10px",paddingRight:"10px"}}>
                    <Searchbar/>
                </div>
            </AppBar >
        </Box >
    );
}
