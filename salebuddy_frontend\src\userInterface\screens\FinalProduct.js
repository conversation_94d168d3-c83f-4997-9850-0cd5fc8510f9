import React, { useEffect, useRef, useState } from 'react'
import ProductInfo from '../components/FinalProduct/ProductInfo'
import ProductPicture from '../components/FinalProduct/ProductPicture'
import { Divider, Grid2, Typography } from '@mui/material'
import CategoryNavigator from '../components/CategoryNavigator'
import Specification from '../components/FinalProduct/Specification'
import { useParams } from 'react-router-dom'
import { postData } from '../../backendservices/FetchNodeServices'

export default function FinalProduct() {
    const { productdetailid, productid } = useParams()
    console.log(`${productdetailid} ${productid}`)
    const [productData, setProductData] = useState({})
    const [productImage, setProductImage] = useState({})
    console.log("aaaaaaaaaa", productImage)
    const fetch_Product_data = async () => {
        const response = await postData("userInterface/userinterface_fetch_productdetail_by_id", { "productdetailid": productdetailid })
        setProductData(response?.data)
    }
    const fetch_Product_image = async () => {
        const response = await postData("userInterface/userinterface_fetch_image_by_id", { "productdetailid": productdetailid })
        setProductImage(response?.data)
    }
    useEffect(() => {
        fetch_Product_data()
        fetch_Product_image()
    }, [productdetailid])

    return (
        <div tabIndex={-1} style={{ maxWidth: "1200px", width: "100%", marginBottom: 30, height: "100%", marginLeft: "auto", marginRight: "auto", color: "white", display: "flex", flexDirection: "column" }}>
            <CategoryNavigator />
            <Grid2 container spacing={4}>
                <Grid2 size={6} sx={{ position: "sticky", top: 100, alignSelf: "flex-start" }}>
                    <ProductPicture data={productImage} />
                </Grid2>
                <Grid2 size={6} >
                    <ProductInfo data={productData} productid={productid} />
                </Grid2>
            </Grid2>
            <Specification />

        </div>
    )
}
