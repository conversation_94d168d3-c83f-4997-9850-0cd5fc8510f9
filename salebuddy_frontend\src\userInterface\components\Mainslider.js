import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import Slider from "react-slick";
import { useRef, useState } from "react";
import useMediaQuery from '@mui/material/useMediaQuery';
import MainSliderBackArrow from "./MainSliderBackArrow";
import MainSliderForwordArrow from "./MainSliderForwardArrow.";
import { serverURL } from "../../backendservices/FetchNodeServices";

export default function MainSlider() {
    const [sliderArrow, setSliderArrow] = useState("");
    const matches = useMediaQuery('(max-width:780px)');
    const sliderRef = useRef(null);

    const settings = {
        infinite: true,
        speed: 500,
        slidesToShow: 1,
        slidesToScroll: 1,
        autoplay: true,
        autoplaySpeed: 5000,
        cssEase: 'ease-in-out',
        arrows: false,
    };

    const data = {
        id: 1,
        images: "b1.webp,b2.webp,b3.webp,b4.webp,b5.webp,b6.webp,b7.webp,b8.webp"
    };
    const images = data.images.split(",");

    return (
        <div style={{ maxWidth: '100vw', position: "relative",boxSizing:"border-box" }}>
            <MainSliderBackArrow sliderRef={sliderRef} />
            <MainSliderForwordArrow sliderRef={sliderRef} />
            <div style={{ width: "100%" }}>
                <Slider ref={sliderRef} {...settings}>
                    {
                        images.map((item, i) => (
                            <div key={i} style={{ width: "100%", boxSizing: "border-box" }}>
                                <img
                                    src={`${serverURL}/images/${item}`}
                                    alt={`slide-${i}`}
                                    style={{
                                        width: "100%",
                                        height: "100%",
                                        display: "block",
                                        objectFit: "cover"
                                    }}
                                />
                            </div>
                        ))
                    }
                </Slider>
            </div>
        </div>
    );
}
