import React, { useState } from 'react'
import ArrowForwardIosIcon from '@mui/icons-material/ArrowForwardIos';
import useMediaQuery from '@mui/material/useMediaQuery'


export default function SliderForwardArrow({sliderRef}) {
    const matches = useMediaQuery('(max-width:980px)');

    const [sliderArrow, setSliderArrow] = useState("0.6")
    return (
        <div  style={{ position: "absolute", zIndex: "1", color: "white", top: "40%", right:20, height: "60px", width: "52px", display: matches ? "none" : "flex", alignItems: "center", justifyContent: 'center', borderTopRightRadius: "10px", borderBottomRightRadius: "10px",cursor:'pointer' }}>
            <ArrowForwardIosIcon style={{ fontSize: "22px", opacity:sliderArrow  }}onMouseOver={() => setSliderArrow("1")} onMouseLeave={() => setSliderArrow("0.6")}  onClick={() => sliderRef.current.slickNext()} />
        </div>
    )
}
