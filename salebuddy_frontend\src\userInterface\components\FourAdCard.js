import React from 'react'
import { Divider } from '@mui/material'
import useMediaQuery from '@mui/material/useMediaQuery';


export default function FourAdCard() {
    const md = useMediaQuery('(max-width:900px)');

    return (
        <div style={{
            background: "#020024",
            background: "linear-gradient(169deg, rgba(2,0,36,1) 0%, rgba(9,9,121,1) 35%, rgba(0,212,255,1) 100%)",
            padding: "20px 15px",
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            position: "relative",
            maxWidth: "290px",
            maxHeight: "365px",
            marginLeft: "7.5px",
            marginRight: "3px",
            boxSizing: "border-box",
            borderRadius: 8,
            fontWeight: 700,
            fontSize: 20,
            gap: md?5:10


        }}>
            <div>Boult</div>
            <div >Apple 15 pro</div>
            <img src='/iphone.webp' style={{ width: md?150:200 }} />
            <Divider variant="middle" orientation="horizontal" sx={{ border: "1px solid white", width: "80%" }} />
            <div style={{ fontWeight: 300, fontSize: 18 }}>Starting at <span style={{ fontWeight: 700, fontSize: 20 }}>&#8377;12000</span></div>

        </div>
    )
}
