import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import Slider from "react-slick";
import { useRef, useState } from "react";
import useMediaQuery from '@mui/material/useMediaQuery';
import MainSliderBackArrow from "./MainSliderBackArrow";
import MainSliderForwordArrow from "./MainSliderForwardArrow.";
import { serverURL } from "../../backendservices/FetchNodeServices";
import ProductCard from "./ProductCard";
import ScrollerBackArrow from "./ScrollerBackArrow";
import ScrollerForwardArrow from "./ScrollerForwardArrow";
import { Grid2, Toolbar, Typography } from "@mui/material";

export default function Scroller({ title, data }) {
    const lg = useMediaQuery('(max-width:1290px)');
    const sm = useMediaQuery('(max-width:500px)');
    const sliderRef = useRef(null);

    const settings = {
        infinite: true,
        speed: 500,
        slidesToShow: sm?2:lg?3:4,
        slidesToScroll: 1,
        cssEase: 'ease-in-out',
        arrows: false,
    };




    return (
        <div style={{ display: "flex", flexDirection: "column", gap: 15, width: "100%" }}>
            <Typography sx={{ paddingLeft: sm?2:5, fontSize:sm?16: 29, fontWeight: 600 }} variant="h4" color="inherit" component="div">
                {title}
            </Typography>
            <div style={{ maxWidth: '100%', boxSizing: "border-box", display: "flex", alignItems: "center" }}>
                <ScrollerBackArrow sliderRef={sliderRef} />
                <div style={{ width: "95%" }}>
                    <Slider ref={sliderRef} {...settings}>
                        {data.map((item, index) => {

                            return (
                                <div>
                                    <ProductCard key={index} data={item} />
                                </div>

                            )
                        })}
                    </Slider>
                </div>
                <div >
                    <ScrollerForwardArrow sliderRef={sliderRef} />
                </div>
            </div>
        </div>
    );
}
