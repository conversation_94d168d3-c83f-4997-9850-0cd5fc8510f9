import { But<PERSON>, Divider, Grid2, Rating, Typography } from '@mui/material'
import React from 'react'
import PercentIcon from '@mui/icons-material/Percent';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import useMediaQuery from '@mui/material/useMediaQuery'
import ListContainer from '../components/Cart/ListContainer';
import OrderSummary from '../components/Cart/OrderSummary';
export default function Cart() {
    const lg = useMediaQuery('(max-width:1024px)');

    return (

        <div style={{ backgroundColor: "#F9F9F9", minHeight: "100vh" ,padding:20}}>
            <div style={{ marginLeft: "auto", marginRight: "auto", maxWidth: 1170, paddingTop: 40, paddingBottom: 20, display: "flex", flexDirection: "column" }}>
                <Typography sx={{ fontSize: 20, fontWeight: 700 }}>YOUR CART</Typography>
                <div style={{ paddingTop: 40, display: "flex", flexGrow: 1,}}>
                    <Grid2 container spacing={2} sx={{width:"100%"}}>
                       <Grid2 size={lg?12:8.5}>
                        <ListContainer/>
                       </Grid2>
                        <Grid2 size={lg?12:3.5} >
                            <OrderSummary/>
                        </Grid2>
                       
                    </Grid2>



                </div>
            </div>


        </div>
    )
}
