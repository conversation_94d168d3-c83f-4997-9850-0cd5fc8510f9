import React, { useEffect, useState } from 'react'
import Header from '../components/Header';
import MainSlider from '../components/Mainslider';
import useMediaQuery from '@mui/material/useMediaQuery'
import Footer from '../components/Footer';
import BrandSlider from '../components/BrandSlider';
import ProductCard from '../components/ProductCard';
import { Grid2, Slide } from '@mui/material';
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import Slider from "react-slick";
import Scroller from '../components/Scroller';
import { getData, postData, serverURL } from '../../backendservices/FetchNodeServices';
import BrandCard from '../components/BrandCard';
import ShowAdvertisment from '../components/ShowAdvertisment';
import ShowMultipleAds from '../components/ShowMultipleAds';
import CategoryCard from '../components/CategoryCard';
import FourAds from '../components/FourAds';

export default function Home() {
  const laptop = useMediaQuery('(max-width:1200px)');
  const [brandList, setBrandList] = useState([])
  const [latestLunchesProduct, setLatestLunchesProduct] = useState([])
  const [summerSpecialProduct, setSummerSpecialProduct] = useState([])



  const fetchBrandList = async () => {
    const response = await getData("userinterface/userinterface_fetch_brands")
    if (response.status) {
      setBrandList(response.data)
    }
    else {
      alert("not fetched")
    }

  }

  const fetchProductByStatus = async (status) => {
    const response = await postData("userinterface/userinterface_fetch_productdetail_by_status", { status })
    if (response.status) {
      switch (status) {
        case "latest launches":
          setLatestLunchesProduct(response.data)
          break;
        case "summer special deals":
          setSummerSpecialProduct(response.data)
          break;
      }
    }
    else {
      alert("not fetched")
    }

  }
  useEffect(() => {
    fetchBrandList();
    fetchProductByStatus("latest launches")
    fetchProductByStatus("summer special deals")

  }, [])
  return (
    <div>
      <div style={{ width: "100%", boxSizing: "border-box" }}>
        <MainSlider />
      </div>
      <div style={{ maxWidth: "1300px", width: "100%", marginLeft: "auto", marginRight: "auto", display: "flex", flexDirection: "column", gap: "20px", color: "white", marginTop: 10 }}>
        <CategoryCard />
        <ShowAdvertisment />
        <Scroller title={"Summer Special Deal"} data={summerSpecialProduct} />

        <ShowMultipleAds NoOfAd={2} adNo={1} />
        <ShowMultipleAds NoOfAd={3} adNo={0} />
        <Scroller title={"Latest Lunches"} data={latestLunchesProduct} />
        <FourAds />
        <BrandSlider data={brandList} />




      </div >
    </div>

  )
}
