import StarIcon from '@mui/icons-material/Star';
import { serverURL } from '../../backendservices/FetchNodeServices';
import useMediaQuery from '@mui/material/useMediaQuery';
import { useTheme } from '@mui/material/styles';
import { useRef } from "react";
import FavoriteBorderIcon from '@mui/icons-material/FavoriteBorder';
import CropSquareIcon from '@mui/icons-material/CropSquare';
import { ForkLeft } from '@mui/icons-material';

export default function Product({ item }) {

    const sliderRef = useRef();
    const theme = useTheme();
    const medium = useMediaQuery(theme.breakpoints.down('675'));
    const large = useMediaQuery(theme.breakpoints.down('lg'));

    return (
        <div style={{ borderBottom:medium?'0.5px solid gray':'', display: 'flex', flexDirection: medium?'row':'column', width:medium?'105%': '29%', height: medium?'300px': '500px', color: '#fff', background: '#191919' }}>

            <div style={{ background: '#393939', height:medium?'90%': '50%', display: 'flex', justifyContent: 'center', alignItems: 'center', borderRadius: '10px' }}>
                <img src={item.image} style={{ width: medium?150: 215  }} />
                
                <div style={{position:medium?'':'absolute' , gap:medium?'':'10px', display:'flex' ,paddingLeft:large?'5%':'10%' , alignItems:'center' , paddingBottom:'195px' }}>
                    <div style={{ display:'flex' ,position:medium?'absolute':"", left:'25%' , justifyContent:'center', marginTop:medium?'-20px':'', alignItems:'center',borderRadius:'100%' ,background: 'rgba(44, 44, 44, 0.8)', padding:6}}><FavoriteBorderIcon/></div>

                    <div style={{display:'flex', position:medium?'absolute':"",  marginLeft:medium?'-138px':'' , marginTop:medium?'410px':'', alignItems:'center' ,borderRadius:'20px', padding:"6px 10px"  ,background: 'rgba(44, 44, 44, 0.8)'}} ><CropSquareIcon/><span>Compare</span></div>
                </div>

                <div></div>

            </div>

            <div style={{borderBottom:medium?'':'1px solid gray',height:'40%' , paddingLeft:medium?20:0,  paddingTop: medium?0:25}}>
            
                <div style={{ fontSize: 18, fontWeight: 'bolder' }} >{item.productname}</div>
               
               
                <div style={{ display: 'flex', gap:5 ,paddingTop:15 }}>
                    <div style={{ display: 'flex', color: '#12DAA8', gap:5 , alignItems:'start' }}>
                        <div style={{ fontWeight: 'bold', fontSize: 15 }}>
                            {item.ratings}
                        </div>
                        <div >
                             <StarIcon style={{fontSize:16}} />
                        </div>
                    </div>
                    <div style={{fontSize:15 , }}>({item.reviews})</div>
                </div>
                
                
                <div style={{fontSize:23 , fontWeight:'bold' , paddingTop:15}} > &#8377;{item.offer} <span style={{opacity:'75%'}}><s>&#8377;{item.price}</s></span></div>
            </div>
        </div>
    )
}