import React, { useEffect, useState } from 'react'
import VerticalSlider from '../VerticalSlider'
import FavoriteBorderIcon from '@mui/icons-material/FavoriteBorder';
import ShareIcon from '@mui/icons-material/Share';
import { Divider } from '@mui/material';
import { serverURL } from '../../../backendservices/FetchNodeServices';
function ProductPicture({ data }) {
  const [verticalImage, setVerticalImage] = useState([])

  // const verticalImage = "947bd157-0741-4f7c-bd22-8c690ea0e364.webp,e16112c8-1cfb-45a4-9ca4-b8a100326f73.png,8c311858-c0c4-42d3-bdd2-24624b8bbc73.webp,df3dbd1f-1c52-4c20-980c-8becd41822bd.webp,8fb9e64b-b833-415a-a7d3-a5d98e2d2d98.webp,b3dd8ef8-99ba-4077-be50-7ccb50f834f8.webp,f05c56de-6799-4af2-89d9-cd41879bb9af.webp,284b37ef-9ae3-41dc-a07f-611f45dc9360.webp,000216b9-9bf8-4550-951f-88c1fb299e82.webp,0dff6468-818c-4c45-8491-da0fb7f8cd18.webp,05e01b6f-3358-4492-a854-5f093615376a.webp".split(",")
  const [selectedImage, setSelectedImage] = useState([])
  useEffect(() => {
    setVerticalImage(data?.picture?.split(","))
    setSelectedImage(data?.picture?.split(",")[0])

  }, [data])

  return (
    <div style={{ position: "sticky", width: "100%", display: "flex", position: "relative" }} >
      <VerticalSlider selectedImage={selectedImage} data={verticalImage} setSelectedImage={setSelectedImage} />
      <div style={{ flexGrow: 1, display: "flex", justifyContent: "center", alignItems: "center" }}>
        {(selectedImage?.includes("webm") || selectedImage?.includes("mp4")) ? (
          <video
            src={`${serverURL}/images/${selectedImage}`}
            width="100%"
            controls
            autoPlay
            muted
            loop
            style={{ objectFit: "contain" }}
          />
        ) : (
          <img
            src={`${serverURL}/images/${selectedImage}`}
            width="100%"
            style={{ objectFit: "contain" }}
          />
        )}
      </div>
      <FavoriteBorderIcon sx={{ position: "absolute", top: 10, right: 40 }} />
      <ShareIcon sx={{ position: "absolute", top: 10, right: 0 }} />
      <div style={{ padding: "60px 0px", marginRight: 2 }}>
        <Divider orientation="vertical" sx={{ background: "white" }} />
      </div>



    </div>
  )
}

export default ProductPicture