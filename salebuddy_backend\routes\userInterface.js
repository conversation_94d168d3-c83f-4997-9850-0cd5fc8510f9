var express = require('express');
const pool = require('./pool');
var router = express.Router();

/* GET users listing. */
router.get('/userinterface_fetch_brands', function (req, res, next) {

    try {

        pool.query("select B.*,S.* from brands B,services S where b.serviceid=S.serviceid", function (error, result) {

            if (error) {
                console.log(error);
                res.status(200).json({ status: false, message: "Database Error, Pls Contact Backend Team" })
            }
            else {
                res.status(200).json({ status: true, message: "Sucess...", data: result })
            }

        })

    }

    catch (e) {
        console.log(error);
        res.status(200).json({ status: false, message: "Critical Error, Pls Contact Server Administrator" })
    }


});
router.post('/userinterface_fetch_ads', function (req, res, next) {

    try {

        pool.query("select * from ads where imageNumber=?", [req.body.imageNumber], function (error, result) {

            if (error) {
                console.log(error);
                res.status(200).json({ status: false, message: "Database Error, Pls Contact Backend Team" })
            }
            else {
                res.status(200).json({ status: true, message: "Sucess...", data: result })
            }

        })

    }

    catch (e) {
        console.log(error);
        res.status(200).json({ status: false, message: "Critical Error, Pls Contact Server Administrator" })
    }


});
router.post('/userinterface_fetch_productdetail_by_status', function (req, res, next) {
    try {
        pool.query(`SELECT pd.*,s.servicetype,s.servicename,s.icon AS service_icon,s.servicestatus,b.brandname,
                b.brandlogo,p.productname,p.productdescription,p.productpicture,pc.productcolor,pc.productcolorname,pv.productram,pv.productstorage
                FROM productdetails pd JOIN services s ON pd.serviceid = s.serviceid
                JOIN brands b ON pd.brandid = b.brandid
                JOIN products p ON pd.productid = p.productid
                JOIN productColors pc ON pd.productcolorid = pc.productcolorid
                JOIN productVarients pv ON pd.productvarientid = pv.productvarientid where status=? `,
            [req.body.status], function (error, result) {

                if (error) {
                    console.log(error);
                    res.status(200).json({ status: false, message: "Database Error, Pls Contact Backend Team" })
                }
                else {
                    res.status(200).json({ status: true, message: "Sucess...", data: result })
                }
            })

    }

    catch (e) {
        console.log(error);
        res.status(200).json({ status: false, message: "Critical Error, Pls Contact Server Administrator" })
    }


});
router.post('/userinterface_fetch_productdetail_by_id', function (req, res, next) {
    try {
        pool.query(`SELECT pd.*,s.servicetype,s.servicename,s.icon AS service_icon,s.servicestatus,b.brandname,
                b.brandlogo,p.productname,p.productdescription,p.productpicture,pc.productcolor,pc.productcolorname,pv.productram,pv.productstorage
                FROM productdetails pd JOIN services s ON pd.serviceid = s.serviceid
                JOIN brands b ON pd.brandid = b.brandid
                JOIN products p ON pd.productid = p.productid
                JOIN productColors pc ON pd.productcolorid = pc.productcolorid
                JOIN productVarients pv ON pd.productvarientid = pv.productvarientid where productdetailid=? `,
            [req.body.productdetailid], function (error, result) {

                if (error) {
                    console.log(error);
                    res.status(200).json({ status: false, message: "Database Error, Pls Contact Backend Team" })
                }
                else {
                    res.status(200).json({ status: true, message: "Sucess...", data: result[0] })
                }
            })

    }

    catch (e) {
        console.log(error);
        res.status(200).json({ status: false, message: "Critical Error, Pls Contact Server Administrator" })
    }


});
router.get('/userinterface_fetch_category', function (req, res, next) {

    try {

        pool.query("select * from services", function (error, result) {

            if (error) {
                console.log(error);
                res.status(200).json({ status: false, message: "Database Error, Pls Contact Backend Team" })
            }
            else {
                res.status(200).json({ status: true, message: "Sucess...", data: result })
            }

        })

    }

    catch (e) {
        console.log(error);
        res.status(200).json({ status: false, message: "Critical Error, Pls Contact Server Administrator" })
    }


});
router.post('/userinterface_fetch_colors', function (req, res, next) {

    try {

        pool.query("select * from productcolors where productid=?", [req.body.productid], function (error, result) {

            if (error) {
                console.log(error);
                res.status(200).json({ status: false, message: "Database Error, Pls Contact Backend Team" })
            }
            else {
                res.status(200).json({ status: true, message: "Sucess...", data: result })
            }

        })

    }

    catch (e) {
        console.log(error);
        res.status(200).json({ status: false, message: "Critical Error, Pls Contact Server Administrator" })
    }


});
router.post('/userinterface_fetch_ram', function (req, res, next) {

    try {

        pool.query("select distinct(productram) from  productvarients where productid=?", [req.body.productid], function (error, result) {

            if (error) {
                console.log(error);
                res.status(200).json({ status: false, message: "Database Error, Pls Contact Backend Team" })
            }
            else {
                res.status(200).json({ status: true, message: "Sucess...", data: result })
            }

        })

    }

    catch (e) {
        console.log(error);
        res.status(200).json({ status: false, message: "Critical Error, Pls Contact Server Administrator" })
    }


});
router.post('/userinterface_fetch_storage', function (req, res, next) {

    try {

        pool.query("select distinct(productstorage) from  productvarients where productid=?", [req.body.productid], function (error, result) {

            if (error) {
                console.log(error);
                res.status(200).json({ status: false, message: "Database Error, Pls Contact Backend Team" })
            }
            else {
                res.status(200).json({ status: true, message: "Sucess...", data: result })
            }

        })

    }

    catch (e) {
        console.log(error);
        res.status(200).json({ status: false, message: "Critical Error, Pls Contact Server Administrator" })
    }


});
router.post('/userinterface_fetch_image_by_id', function (req, res, next) {
    try {
        pool.query(`SELECT * from morepicture where productdetailid=? `,
            [req.body.productdetailid], function (error, result) {

                if (error) {
                    console.log(error);
                    res.status(200).json({ status: false, message: "Database Error, Pls Contact Backend Team" })
                }
                else {
                    console.log(result);
                    res.status(200).json({ status: true, message: "Sucess...", data: result[0] })
                }
            })

    }

    catch (e) {
        console.log(error);
        res.status(200).json({ status: false, message: "Critical Error, Pls Contact Server Administrator" })
    }


});
router.post('/userinterface_fetch_productdetailid_by_product&color', function (req, res, next) {

    try {

        pool.query(`SELECT * FROM productdetails PD inner join productcolors PC on PD.productcolorid=PC.productcolorid 
                    where PD.productid=? and PC.productcolorname=?`,
            [req.body.productid, req.body.data], function (error, result) {
                if (error) {
                    console.log(error);
                    res.status(400).json({ status: false, message: "Database Error, Pls Contact Backend Team" })
                }
                else {
                    console.log(result)
                    res.status(200).json({ status: true, message: "Sucess...", data: result[0] })
                }

            })

    }

    catch (e) {
        console.log(error);
        res.status(400).json({ status: false, message: "Critical Error, Pls Contact Server Administrator" })
    }


});

module.exports = router;