import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import Slider from "react-slick";
import { useRef, useState } from "react";
import useMediaQuery from '@mui/material/useMediaQuery';
import MainSliderBackArrow from "./MainSliderBackArrow";
import MainSliderForwordArrow from "./MainSliderForwardArrow.";
import { serverURL } from "../../backendservices/FetchNodeServices";
import ScrollerBackArrow from "./ScrollerBackArrow";
import ScrollerForwardArrow from "./ScrollerForwardArrow";
import BrandCard from "./BrandCard";

export default function BrandSlider({data}) {
    const [sliderArrow, setSliderArrow] = useState("");
    const matches = useMediaQuery('(max-width:780px)');
    const sliderRef = useRef(null);

    const settings = {
        infinite: true,
        speed: 500,
        slidesToShow: matches?3:5,
        slidesToScroll: 1,
        cssEase: 'ease-in-out',
        arrows: false,
    };

  

    return (
        <div style={{ maxWidth: '100%',boxSizing:"border-box",display:"flex" ,alignItems:"center"}}>
           <ScrollerBackArrow sliderRef={sliderRef}/>
            <div style={{ width: "94%",marginLeft:"auto",marginRight:"auto" }}>
                <Slider ref={sliderRef} {...settings}>
                    {
                        data.map((item, i) => (
                              <BrandCard logo={item.brandlogo}/>
                        ))
                    }
                </Slider>
            </div>
            <ScrollerForwardArrow sliderRef={sliderRef}/>
        </div>
    );
}
