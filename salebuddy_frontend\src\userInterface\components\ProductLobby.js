import React, { useState } from "react";
import Product from "./Product";
import Slider from "react-slick";
import useMediaQuery from '@mui/material/useMediaQuery';
import { useTheme } from '@mui/material/styles';
import { useRef } from "react";

export default function ProductsLobby({ data }) {

    const sliderRef = useRef()
    const theme = useTheme();
    const large = useMediaQuery(theme.breakpoints.down('lg'));
    
    


    const [showAll, setShowAll] = useState(false);
    const productsPerRow = 3;
    const visibleRows = 3;
    const visibleCount = productsPerRow * visibleRows;

    const showProduct = () => {
        const itemsToShow = showAll ? data : data.slice(0, visibleCount);
        return itemsToShow.map((item, i) => (
            <Product key={i} item={item} />
        ));
    };

    const handleToggle = () => {
        setShowAll(!showAll);
    };

    return (
        <div style={{ width: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center', background: '#191919', flexDirection: 'column' }}>
            <div style={{ width:large ? "95%" :"1220px", display: 'flex', flexWrap: 'wrap', gap: 40, background: '#191919', justifyContent: 'center' }}>
                {showProduct()}
            </div>
            {data.length > visibleCount && (
                <button
                    onClick={handleToggle}
                    style={{
                        marginTop: 20,
                        padding: '12px 35px',
                        background:'#191919',
                        border: '1px solid #fff',
                        color:'#fff',
                        borderRadius: 8,
                        cursor: 'pointer',
                        fontWeight: 'bold'
                    }}
                >
                    {showAll ? "View Less" : "View More"}
                </button>
            )}
        </div>
    );
}