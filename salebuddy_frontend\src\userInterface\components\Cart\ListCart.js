import { Button, Divider, Rating, Typography } from '@mui/material'
import React from 'react'
import useMediaQuery from '@mui/material/useMediaQuery'


export default function ListCart() {
    const lg = useMediaQuery('(max-width:1024px)');

    return (
        <div style={{ background: "#FFFFFF", display: "flex", marginTop: "30px", padding: 8, borderRadius: 8, paddingBottom: 70 }}>
            <div style={{ display: "flex", alignItems: "center", justifyContent: "center" }}>
                <img src="/iphone.webp" style={{ width: 140 }} />
            </div>
            <div style={{ paddingTop: 25, paddingLeft: 20, maxWidth: 350, display: "flex", flexDirection: "column", gap: 10 }}>
                <Typography sx={{ fontWeight: 600 }}>
                    SAMSUNG Galaxy S25 Edge 5g(12GB RAM, 512GB, Titanium jetblack)
                </Typography>
                <Rating readOnly value={5} sx={{ fontSize: 16 }} />
                <div>
                    <Typography sx={{ fontSize: 14 }}>Standard Delivery by</Typography>
                    <Typography sx={{ fontSize: 14, }}>2 July 2025 | ₹49</Typography>
                </div>
                <div style={{ display: "flex", gap: 10 }}>
                    <Button variant="outlined" sx={{ color: "black", border: "1px solid black", borderRadius: 2, fontSize: 12, fontWeight: 600, opacity: "0.8", paddingX: 5 }}>Move to Wishlist</Button>
                    <Button variant="outlined" sx={{ color: "black", border: "1px solid black", borderRadius: 2, fontSize: 12, fontWeight: 600, opacity: "0.8", paddingX: 5 }}>Remove</Button>
                </div>
            </div>

            <div style={{ textAlign: "right", flexGrow: 1, paddingTop: 20, paddingRight: 10 }}>
                <Typography sx={{ fontWeight: 700, fontSize: 25 }}>₹1200000</Typography>
                <Typography>(Incl. all Taxes)</Typography>
                <Divider variant="inset" sx={{ background: "black", marginTop: 1, marginLeft: 15 }} />
                <Typography sx={{ fontWeight: 700, fontSize: 20, marginTop: 1 }}>₹6943/mo*</Typography>
                <Typography sx={{ fontSize: 12, color: "#08847A", textDecoration: "underline" }}>EMI Options</Typography>


            </div>


        </div>
    )
}
