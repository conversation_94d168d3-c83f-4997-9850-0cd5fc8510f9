import { Typography } from '@mui/material'
import React, { useEffect, useState } from 'react'
import { getData, postData, serverURL } from '../../backendservices/FetchNodeServices';

export default function CategoryCard() {
    const [category,setCategory]=useState([]);
    console.log(category)
    const fetchCategory=async()=>{
        const response=await getData("userInterface/userinterface_fetch_category")
        if (response?.status){
            setCategory(response.data)
        }
        else{
            alert("There is Error")
        }
    }
    useEffect(()=>{
        fetchCategory();
    },[])
    return (
        <div style={{ display: "flex",justifyContent:"space-between",padding:"0 50px"}}>
            { 
                category?.map((item) => {

                    return (
                      
                            <div style={{display:"flex",flexDirection:"column",textAlign:"center",gap:12}}> 
                                <img src={`${serverURL}/images/${item.icon}`} style={{maxWidth:200,width:"100%"}} />
                                <span style={{fontWeight:800}}>{item.servicetype} {item.servicename}</span>
                            </div>
                
                    )
                })
            }
        </div>
    )
}
