import React, { useState } from 'react'
import ArrowBackIosNewIcon from '@mui/icons-material/ArrowBackIosNew';
import useMediaQuery from '@mui/material/useMediaQuery'


export default function SliderBackArrow({sliderRef}) {
    const matches = useMediaQuery('(max-width:980px)');

    const [sliderArrow, setSliderArrow] = useState("0.6")
    return (
        <div onClick={() => sliderRef.current.slickPrev()}   style={{ position: "absolute", zIndex: "1", color: "white", top: "40%", left:20, height: "60px", width: "52px", display: matches ? "none" : "flex", alignItems: "center", justifyContent: 'center', borderTopRightRadius: "10px", borderBottomRightRadius: "10px",cursor:'pointer' }}>
            <ArrowBackIosNewIcon style={{ fontSize: "22px", opacity:sliderArrow  }}onMouseOver={() => setSliderArrow("1")} onMouseLeave={() => setSliderArrow("0.6")}  onClick={() => sliderRef.current.slickPrev()} />
        </div>
    )
}
