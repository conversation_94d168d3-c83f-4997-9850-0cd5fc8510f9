
import { Reviews } from "@mui/icons-material";
import DropDowns from "./dropdowns";
import ProductsLobby from "./ProductLobby";



export default function ProductScreen() {


    var data = [
        {
            productdetailid: 1,
            productname: "Vivo T3 5G(6GB RAM, 128GB, Light Green)",
            variant: "256 GB",
            color: "Teal",
            image: "https://i.ibb.co/ccv1fdRB/product1.webp",
            ratings: 5,
            reviews:100,
            price: '19,999',
            offer: '24,999.0',
        },
        {
            productdetailid: 1,
            productname: "Realme 14x 5G (6GB RAM, 128GB, Golden Glow)",
            variant: "128 GB",
            color: "Teal",
            image: "https://i.ibb.co/C5WPbwtC/realme.webp",
            ratings: 1,
            reviews:900,
            price: '14,999',
            offer: '19,999',
        },
        {
            productdetailid: 1,
            productname: "Nothing CMF Phone 1 5G (6GB RAM, 128GB, Black)",
            variant: "128 GB",
            color: "Teal",
            image: "https://i.ibb.co/RT306fm6/nothing.webp",
            ratings: 5,
            reviews:10,
            price: '21,999',
            offer: '24,999',
        },
        {
            productdetailid: 1,
            productname: "Apple iPhone 16 (6GB RAM, 128GB, Orchid Pink)",
            variant: "128 GB",
            color: "Teal",
            image: "https://i.ibb.co/mrdNKNp2/iphone.png",
            ratings: 4,
            reviews:50,
            price: '78,000',
            offer: '70,000',
        },
        {
            productdetailid: 1,
            productname: "Redmi 13 5G (6GB RAM, 128GB, Orchid Pink)",
            variant: "128 GB",
            color: "Teal",
            image: "https://i.ibb.co/bygK1gc/redmi.webp",
            ratings: 2,
            reviews:999,
            price: '16,999',
            offer: '19,999',
        },
        {
            productdetailid: 1,
            productname: "Apple iPad 10th Generation Wi-Fi (10.9 Inch)",
            variant: "128 GB",
            color: "Teal",
            image: "https://i.ibb.co/84cyDq7d/ipad.webp",
            ratings: 3,
            reviews:40,
            price: '34,999',
            offer: '39,999',
        },

        {
            productdetailid: 1,
            productname: "SAMSUNG Galaxy S24 Ultra 5G (12GB RAM, 256GB, Titanium Gray)",
            variant: "128 GB",
            color: "Green",
            image: "https://i.ibb.co/V0YRLc2R/s24.webp",
            ratings: 5,
            reviews:40,
            price: '1,44,999',
            offer: '1,19,999',
        },

        {
            productdetailid: 1,
            productname: "Google Pixel 9 Pro XL 5G (16GB RAM, 256GB, Porcelain)",
            variant: "128 GB",
            color: "Teal",
            image: "https://i.ibb.co/WWQ8mpQ1/google.webp",
            ratings: 3,
            reviews:40,
            price: '1,29,999',
            offer: '1,04,999',
        },

        {
            productdetailid: 1,
            productname: "SAMSUNG Galaxy S25 5G (12GB RAM, 256GB, Icyblue)",
            variant: "128 GB",
            color: "Teal",
            image: "https://i.ibb.co/mCZ6K5Dw/samsung.webp",
            ratings: 4.5,
            reviews:480,
            price: '99,999',
            offer: '80,999',
        },

        {
            productdetailid: 1,
            productname: "Google Pixel 8a 5G (8GB RAM, 128GB, Obsidian)",
            variant: "128 GB",
            color: "Teal",
            image: "https://i.ibb.co/Mk8q4YjW/google8a.webp",
            ratings: 3.9,
            reviews:400,
            price: '39,999',
            offer: '37,999',
        },

        {
            productdetailid: 1,
            productname: "SAMSUNG Galaxy F15 5G (6GB RAM, 128GB, Light Violet)",
            variant: "128 GB",
            color: "Teal",
            image: "https://i.ibb.co/WQvdtdB/f15.webp",
            ratings: 4.3,
            reviews:600,
            price: '16,999',
            offer: '14,999',
        },

        {
            productdetailid: 1,
            productname: "OnePlus 13R 5G (12GB RAM, 256GB, Nebula Noir)",
            variant: "128 GB",
            color: "Teal",
            image: "https://i.ibb.co/9kQx0cH0/opneplus13.webp",

            
            ratings: 3,
            reviews:99,
            price: '44,999',
            offer: '42,999',
        },
    ];



    return (
        <div style={{ overflowX: 'hidden', background: '#191919', margin: 0, padding: 0, scrollbarWidth: 'none' }}>

           

            <div style={{ paddingTop:'30px',  display:'flex', justifyContent:'center'}}> 
                <DropDowns/>
            </div>

            <div >
                <ProductsLobby data={data} />
            </div>


        </div>
    )
}