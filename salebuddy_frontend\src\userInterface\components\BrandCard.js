import { Box, Card } from '@mui/material'
import React from 'react'
import { serverURL } from '../../backendservices/FetchNodeServices'
import { useNavigate } from "react-router-dom"

export default function BrandCard({logo,url}) {
    return (
    <div style={{ display: "flex", flexDirection: "column", alignItems: "center", marginLeft: "7.5px", marginRight: "7.5px", boxSizing: "border-box" }}>
         <img src={`${serverURL}/images/${logo}`} style={{width:"100%",borderRadius:6,marginLeft:3}}  />
       </div>
    )
}
