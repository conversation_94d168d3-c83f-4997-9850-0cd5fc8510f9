import React, { useEffect, useState } from 'react'
import { postData, serverURL } from '../../backendservices/FetchNodeServices'
import { Typography } from '@mui/material'
import useMediaQuery from '@mui/material/useMediaQuery';


export default function ShowAdvertisment() {
    const lg = useMediaQuery('(max-width:1290px)');
    const sm = useMediaQuery('(max-width:500px)');
    const [oneImage, setOneImage] = useState("")
    const fetchAds = async () => {
        const response = await postData("userInterface/userinterface_fetch_ads", { imageNumber: 1 })
        if (response?.status) {
            setOneImage(response.data)
        }
    }
    useEffect(() => {
        fetchAds()
    }, [])
    return (
        <div style={{ width: "94%", marginLeft: "auto", marginRight: "auto", display: "flex", flexDirection: "column", gap: 15 }}>

            <Typography sx={{ paddingLeft: sm ? 2 : 0, fontSize: sm ? 16 : 29, fontWeight: 600 }} variant="h4" color="inherit" component="div">
                {oneImage[0]?.description}
            </Typography>

            <img src={`${serverURL}/images/${oneImage[0]?.picture}`} width={"100%"} style={{ objectFit: "cover", maxHeight: 300 }} />
        </div>
    )
}
