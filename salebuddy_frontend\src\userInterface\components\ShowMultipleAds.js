import React, { useEffect, useState } from 'react'
import { postData, serverURL } from '../../backendservices/FetchNodeServices'
import { Grid2, Typography } from '@mui/material'
import useMediaQuery from '@mui/material/useMediaQuery';

export default function ShowMultipleAds({ NoOfAd, adNo }) {
    const [adImage, setAdImage] = useState("")
    const lg = useMediaQuery('(max-width:1290px)');
    const sm = useMediaQuery('(max-width:500px)');
    const fetchAds = async () => {
        const response = await postData("userInterface/userinterface_fetch_ads", { imageNumber: NoOfAd })
        if (response?.status) {
            setAdImage(response.data)
        }
    }

    const image = adImage[adNo]?.picture.split(",")
    useEffect(() => {
        fetchAds()
    }, [])
    return (
        <div style={{ width: "94%", marginLeft: "auto", marginRight: "auto", display: "flex", flexDirection: "column", gap: 10 }}>

            <Typography sx={{  paddingLeft: sm ? 2 : 5, fontSize: sm ? 16 : 29, fontWeight: 600, paddingLeft: 1 }} variant="h4" color="inherit" component="div">
                {adImage[adNo]?.description}
            </Typography>



            <div style={{ display: "flex", alignItems: "center" }}>                {
                image?.map((item) => {
                    return (
                        <div style={{ padding: 8 }}>
                            <img src={`${serverURL}/images/${item}`} width={"100%"} style={{ objectFit: "cover" }} />
                        </div>
                    )
                })
            }
            </div>

        </div >
    )
}
